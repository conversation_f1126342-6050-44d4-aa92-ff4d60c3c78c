#!/bin/bash
# 量化分析系统便捷执行脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 进入项目根目录
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "量化分析系统便捷执行脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "可用命令:"
    echo "  demo                    运行快速演示"
    echo "  collect [symbols]       数据采集 (可选指定股票代码，逗号分隔)"
    echo "  indicators [symbols] [--force]  计算指标 (--force 强制重新计算)"
    echo "  patterns [symbols] [--signal TYPE]  形态分析 (--signal 指定信号类型)"
    echo "  daily                   每日更新"
    echo "  maintenance             系统维护"
    echo "  scheduler               启动定时任务调度器"
    echo "  status                  查看系统状态"
    echo "  init                    初始化系统环境"
    echo "  logs                    查看最新日志"
    echo "  help                    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 demo                 # 运行快速演示"
    echo "  $0 collect 000001,000002 # 采集指定股票数据"
    echo "  $0 indicators --force   # 强制重新计算所有指标"
    echo "  $0 indicators 000001,000002 # 计算指定股票指标"
    echo "  $0 patterns --signal BULLISH # 分析看涨形态"
    echo "  $0 patterns 000001 --signal BEARISH # 分析指定股票看跌形态"
    echo "  $0 daily                # 执行每日更新"
    echo "  $0 scheduler            # 启动定时任务"
    echo "  $0 init                 # 初始化系统环境"
    echo "  $0 logs                 # 查看最新日志"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装或不在PATH中"
        exit 1
    fi

    # 检查必要的包
    print_info "检查Python依赖包..."
    python3 -c "
import sys
missing_packages = []
try:
    import pandas
except ImportError:
    missing_packages.append('pandas')
try:
    import numpy
except ImportError:
    missing_packages.append('numpy')
try:
    import duckdb
except ImportError:
    missing_packages.append('duckdb')
try:
    import akshare
except ImportError:
    missing_packages.append('akshare')

if missing_packages:
    print(f'缺少以下包: {missing_packages}')
    sys.exit(1)
else:
    print('所有必要的包都已安装')
    sys.exit(0)
" 2>/dev/null

    if [ $? -ne 0 ]; then
        print_warning "缺少必要的Python包，请运行: pip install -r requirements.txt"
        return 1
    fi

    print_success "Python环境检查通过"
    return 0
}

# 运行快速演示
run_demo() {
    print_info "运行快速演示..."
    python3 app/strategy/examples/quick_demo.py
}

# 数据采集
run_collect() {
    local symbols="$1"
    print_info "开始数据采集..."
    
    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py data-collection --symbols "$symbols"
    else
        python3 app/strategy/scripts/main.py data-collection --all
    fi
}

# 指标计算
run_indicators() {
    local symbols=""
    local force_flag=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_flag="--force"
                shift
                ;;
            *)
                if [ -z "$symbols" ]; then
                    symbols="$1"
                fi
                shift
                ;;
        esac
    done

    print_info "开始指标计算..."
    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py indicators --symbols "$symbols" --batch-size 50 --workers 4 $force_flag
    else
        python3 app/strategy/scripts/main.py indicators --all --batch-size 50 --workers 4 $force_flag
    fi
}

# 形态分析
run_patterns() {
    local signal_flag=""
    local symbols=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --signal)
                if [ -n "$2" ]; then
                    signal_flag="--signal-type $2"
                    shift 2
                else
                    print_error "参数 --signal 需要指定信号类型"
                    return 1
                fi
                ;;
            *)
                if [ -z "$symbols" ]; then
                    symbols="$1"
                fi
                shift
                ;;
        esac
    done

    print_info "开始形态分析..."
    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py patterns --symbols "$symbols" $signal_flag
    else
        python3 app/strategy/scripts/main.py patterns --all $signal_flag
    fi
}

# 每日更新
run_daily() {
    print_info "开始每日更新..."
    python3 app/strategy/scripts/main.py daily-update
}

# 系统维护
run_maintenance() {
    print_info "开始系统维护..."
    python3 app/strategy/scripts/main.py maintenance --cleanup --optimize --check-health
}

# 启动调度器
run_scheduler() {
    print_info "启动定时任务调度器..."
    python3 app/strategy/scripts/scheduler.py
}

# 查看系统状态
show_status() {
    print_info "查看系统状态..."

    # 检查数据目录
    if [ -d "data/strategy" ]; then
        print_success "数据目录存在"

        # 统计数据文件
        parquet_count=$(find "data/strategy" -name "*.parquet" 2>/dev/null | wc -l)
        print_info "Parquet文件数量: $parquet_count"

        # 检查数据库
        if [ -f "data/strategy/strategy.duckdb" ]; then
            print_success "数据库文件存在"
            # 获取数据库文件大小
            db_size=$(du -h "data/strategy/strategy.duckdb" 2>/dev/null | cut -f1)
            print_info "数据库文件大小: $db_size"
        else
            print_warning "数据库文件不存在"
        fi
    else
        print_warning "数据目录不存在，将创建目录结构..."
        mkdir -p "data/strategy/logs"
        mkdir -p "data/strategy/cache"
        print_success "目录结构已创建"
    fi

    # 检查日志
    if [ -d "data/strategy/logs" ]; then
        print_success "日志目录存在"
        log_count=$(find "data/strategy/logs" -name "*.log" 2>/dev/null | wc -l)
        print_info "日志文件数量: $log_count"

        # 显示最新日志文件
        latest_log=$(find "data/strategy/logs" -name "*.log" -type f -exec ls -t {} + 2>/dev/null | head -1)
        if [ -n "$latest_log" ]; then
            print_info "最新日志文件: $(basename "$latest_log")"
        fi
    else
        print_warning "日志目录不存在"
    fi

    # 检查Python环境
    print_info "检查Python环境..."
    check_python

    # 运行Python状态检查
    print_info "检查数据管理器状态..."
    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from app.strategy.data.manager import DataManager
    manager = DataManager()
    symbols = manager.get_available_symbols()
    print(f'✓ 可用股票数量: {len(symbols)}')

    # 检查最近更新时间
    from pathlib import Path
    db_path = Path('data/strategy/strategy.duckdb')
    if db_path.exists():
        import datetime
        mtime = datetime.datetime.fromtimestamp(db_path.stat().st_mtime)
        print(f'✓ 数据库最后更新: {mtime.strftime(\"%Y-%m-%d %H:%M:%S\")}')

except ImportError as e:
    print(f'❌ 导入模块失败: {e}')
    print('请确保已安装所有依赖包')
except Exception as e:
    print(f'❌ 状态检查失败: {e}')
" 2>/dev/null || print_warning "Python状态检查失败"
}

# 初始化系统环境
init_system() {
    print_info "初始化系统环境..."

    # 创建必要的目录结构
    print_info "创建目录结构..."
    mkdir -p "data/strategy/logs"
    mkdir -p "data/strategy/cache"
    mkdir -p "data/strategy/backups"
    print_success "目录结构创建完成"

    # 检查Python环境
    print_info "检查Python环境..."
    if ! check_python; then
        print_error "Python环境检查失败，请先安装必要的依赖包"
        return 1
    fi

    # 初始化数据管理器
    print_info "初始化数据管理器..."
    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from app.strategy.data.manager import DataManager
    manager = DataManager()
    print('✓ 数据管理器初始化成功')
except Exception as e:
    print(f'❌ 数据管理器初始化失败: {e}')
    sys.exit(1)
" || return 1

    print_success "系统环境初始化完成"
}

# 查看最新日志
show_logs() {
    print_info "查看最新日志..."

    if [ ! -d "data/strategy/logs" ]; then
        print_warning "日志目录不存在"
        return 1
    fi

    # 查找最新的日志文件
    latest_log=$(find "data/strategy/logs" -name "*.log" -type f -exec ls -t {} + 2>/dev/null | head -1)

    if [ -z "$latest_log" ]; then
        print_warning "没有找到日志文件"
        return 1
    fi

    print_info "显示最新日志文件: $(basename "$latest_log")"
    echo "----------------------------------------"
    tail -50 "$latest_log"
    echo "----------------------------------------"
    print_info "显示最后50行，完整日志请查看: $latest_log"
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    print_error "脚本在第 $line_number 行发生错误，退出码: $exit_code"
    exit $exit_code
}

# 设置错误处理
trap 'handle_error $LINENO' ERR

# 主逻辑
main() {
    # 记录开始时间
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    print_info "脚本开始执行: $start_time"

    # 确保在项目根目录
    cd "$PROJECT_ROOT"

    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        return 0
    fi

    # 检查Python环境（除了help和init命令）
    if [ "$1" != "help" ] && [ "$1" != "--help" ] && [ "$1" != "-h" ] && [ "$1" != "init" ]; then
        if ! check_python; then
            print_error "Python环境检查失败，请运行 '$0 init' 初始化环境"
            return 1
        fi
    fi

    # 解析命令
    case "$1" in
        "demo")
            run_demo
            ;;
        "collect")
            run_collect "$2"
            ;;
        "indicators")
            shift
            run_indicators "$@"
            ;;
        "patterns")
            shift
            run_patterns "$@"
            ;;
        "daily")
            run_daily
            ;;
        "maintenance")
            run_maintenance
            ;;
        "scheduler")
            run_scheduler
            ;;
        "status")
            show_status
            ;;
        "init")
            init_system
            ;;
        "logs")
            show_logs
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac

    # 记录结束时间
    local end_time=$(date '+%Y-%m-%d %H:%M:%S')
    print_success "脚本执行完成: $end_time"
}

# 执行主函数
main "$@"
